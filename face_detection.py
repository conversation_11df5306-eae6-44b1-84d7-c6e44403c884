from ultralytics import YOLO
import cv2
from deepface import DeepFace
import os

def process_frame(frame, model):
    """Process a single frame for face detection and recognition"""
    print("Running YOLO detection...")
    # Run YOLO detection
    results = model(frame, verbose=False)
    print(f"YOLO detected {len(results[0].boxes) if results[0].boxes is not None else 0} objects")

    if results[0].boxes is not None:
        for box in results[0].boxes:
            # Check if detected object is a person (class 0)
            detected_class = int(box.cls[0])
            print(f"Detected object class: {detected_class}")
            if detected_class == 0:  # Person class
                print("Found a person! Processing face...")
                x1, y1, x2, y2 = map(int, box.xyxy[0])
                # Extract face region (assuming upper part of person detection)
                face_height = int((y2 - y1) * 0.3)  # Take top 30% as face region
                face = frame[y1:y1+face_height, x1:x2]

                name = "Unknown"
                if face.size != 0:  # avoid empty crop
                    try:
                        # Search in face DB
                        recognition = DeepFace.find(
                            face,
                            db_path="faces_db/",
                            enforce_detection=False,
                            silent=True
                        )

                        if len(recognition) > 0 and not recognition[0].empty:
                            identity_path = recognition[0].iloc[0]["identity"]
                            name = os.path.basename(os.path.dirname(identity_path))

                    except Exception as e:
                        print("Recognition error:", e)

                # Draw bounding box + label
                cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
                cv2.putText(frame, name, (x1, y1 - 10),
                            cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)

    return frame

def main():
    # Load YOLO model for object detection (will detect persons)
    print("Loading YOLO model...")
    model = YOLO("yolov8n.pt")

    # Test with image file instead of webcam
    test_image_path = "test_image.jpg"  # You can change this to any image file
    use_webcam = False

    # Check if test image exists, otherwise use webcam
    if os.path.exists(test_image_path):
        frame = cv2.imread(test_image_path)
        if frame is None:
            print(f"Could not load image: {test_image_path}")
            print("Trying webcam instead...")
            use_webcam = True
        else:
            print(f"Using test image: {test_image_path}")
    else:
        print("No test image found, trying webcam...")
        use_webcam = True

    if use_webcam:
        print("Initializing webcam...")
        cap = cv2.VideoCapture(0)
        if not cap.isOpened():
            print("Could not access webcam!")
            return

        print("Webcam initialized. Press 'q' to quit.")
        while True:
            ret, frame = cap.read()
            if not ret:
                print("Could not read frame from webcam!")
                break

            # Process frame
            processed_frame = process_frame(frame, model)

            # Display result
            cv2.imshow("Face Detection + Recognition", processed_frame)

            # Check for quit
            if cv2.waitKey(1) & 0xFF == ord('q'):
                break

        cap.release()
    else:
        # Process single image
        print("Processing image...")
        processed_frame = process_frame(frame, model)

        # Display result
        cv2.imshow("Face Detection + Recognition", processed_frame)
        print("Press any key to close the window...")
        cv2.waitKey(0)  # Wait for any key press
        print("Detection completed!")

    cv2.destroyAllWindows()

if __name__ == "__main__":
    main()
