from ultralytics import YOLO
import cv2
from deepface import DeepFace
import os

# Load YOLO face model (download yolov8n-face.pt first)
model = YOLO("yolov8n-face.pt")

# Start webcam
cap = cv2.VideoCapture(0)

while True:
    ret, frame = cap.read()
    if not ret:
        break

    # Run YOLO detection
    results = model(frame, verbose=False)

    for box in results[0].boxes:
        x1, y1, x2, y2 = map(int, box.xyxy[0])
        face = frame[y1:y2, x1:x2]

        name = "Unknown"
        if face.size != 0:  # avoid empty crop
            try:
                # Search in face DB
                recognition = DeepFace.find(
                    face,
                    db_path="faces_db/",
                    enforce_detection=False,
                    silent=True
                )

                if len(recognition) > 0 and not recognition[0].empty:
                    identity_path = recognition[0].iloc[0]["identity"]
                    name = os.path.basename(os.path.dirname(identity_path))

            except Exception as e:
                print("Recognition error:", e)

        # Draw bounding box + label
        cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
        cv2.putText(frame, name, (x1, y1 - 10),
                    cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)

    cv2.imshow("Face Detection + Recognition", frame)

    if cv2.waitKey(1) & 0xFF == ord('q'):
        break

cap.release()
cv2.destroyAllWindows()
