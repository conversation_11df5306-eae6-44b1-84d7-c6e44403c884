"""AUTOGENERATED. DO NOT EDIT."""

from tensorflow.python.ops.init_ops import Constant
from tensorflow.python.ops.init_ops import Constant as constant
from tensorflow.python.ops.init_ops import GlorotNormal as glorot_normal
from tensorflow.python.ops.init_ops import GlorotUniform as glorot_uniform
from tensorflow.python.ops.init_ops import Identity
from tensorflow.python.ops.init_ops import Identity as identity
from tensorflow.python.ops.init_ops import Ones
from tensorflow.python.ops.init_ops import Ones as ones
from tensorflow.python.ops.init_ops import Orthogonal
from tensorflow.python.ops.init_ops import Orthogonal as orthogonal
from tensorflow.python.ops.init_ops import VarianceScaling
from tensorflow.python.ops.init_ops import Zeros
from tensorflow.python.ops.init_ops import Zeros as zeros
from tf_keras.src.initializers import deserialize
from tf_keras.src.initializers import get
from tf_keras.src.initializers import serialize
from tf_keras.src.initializers.initializers import Initializer
from tf_keras.src.initializers.initializers_v1 import HeNormal as he_normal
from tf_keras.src.initializers.initializers_v1 import HeUniform as he_uniform
from tf_keras.src.initializers.initializers_v1 import LecunNormal as lecun_normal
from tf_keras.src.initializers.initializers_v1 import LecunUniform as lecun_uniform
from tf_keras.src.initializers.initializers_v1 import RandomNormal
from tf_keras.src.initializers.initializers_v1 import RandomNormal as normal
from tf_keras.src.initializers.initializers_v1 import RandomNormal as random_normal
from tf_keras.src.initializers.initializers_v1 import RandomUniform
from tf_keras.src.initializers.initializers_v1 import RandomUniform as random_uniform
from tf_keras.src.initializers.initializers_v1 import RandomUniform as uniform
from tf_keras.src.initializers.initializers_v1 import TruncatedNormal
from tf_keras.src.initializers.initializers_v1 import TruncatedNormal as truncated_normal
